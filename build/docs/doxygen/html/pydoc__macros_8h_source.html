<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>GNU Radio&#39;s SC_CODER Package: pydoc_macros.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">GNU Radio&#39;s SC_CODER Package
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('pydoc__macros_8h_source.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">pydoc_macros.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="pydoc__macros_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor">#ifndef PYDOC_MACROS_H</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="preprocessor">#define PYDOC_MACROS_H</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160; </div>
<div class="line"><a name="l00004"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#afa7cf06549cf7fd295f622ef6f376357">    4</a></span>&#160;<span class="preprocessor">#define __EXPAND(x) x</span></div>
<div class="line"><a name="l00005"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a251b23a2efed48ff2ba2d6f449ed33a3">    5</a></span>&#160;<span class="preprocessor">#define __COUNT(_1, _2, _3, _4, _5, _6, _7, COUNT, ...) COUNT</span></div>
<div class="line"><a name="l00006"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a2815f7ab197591ba9559415c694f9ce0">    6</a></span>&#160;<span class="preprocessor">#define __VA_SIZE(...) __EXPAND(__COUNT(__VA_ARGS__, 7, 6, 5, 4, 3, 2, 1))</span></div>
<div class="line"><a name="l00007"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#afdd58e017733f2235c227ef8dd3b2de8">    7</a></span>&#160;<span class="preprocessor">#define __CAT1(a, b) a##b</span></div>
<div class="line"><a name="l00008"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#af08fe30f2a41cef10658f6a892d20543">    8</a></span>&#160;<span class="preprocessor">#define __CAT2(a, b) __CAT1(a, b)</span></div>
<div class="line"><a name="l00009"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a43c147206df4346db5e9d6445aab28ae">    9</a></span>&#160;<span class="preprocessor">#define __DOC1(n1) __doc_##n1</span></div>
<div class="line"><a name="l00010"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a53738db606593bc1554371d718695fed">   10</a></span>&#160;<span class="preprocessor">#define __DOC2(n1, n2) __doc_##n1##_##n2</span></div>
<div class="line"><a name="l00011"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a21baf08c04d32485764f227c32b5bf5d">   11</a></span>&#160;<span class="preprocessor">#define __DOC3(n1, n2, n3) __doc_##n1##_##n2##_##n3</span></div>
<div class="line"><a name="l00012"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a9463ba0ad86bcb4c59b5aecf8a37174c">   12</a></span>&#160;<span class="preprocessor">#define __DOC4(n1, n2, n3, n4) __doc_##n1##_##n2##_##n3##_##n4</span></div>
<div class="line"><a name="l00013"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a942579d548421ddb2906af7094e5789c">   13</a></span>&#160;<span class="preprocessor">#define __DOC5(n1, n2, n3, n4, n5) __doc_##n1##_##n2##_##n3##_##n4##_##n5</span></div>
<div class="line"><a name="l00014"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a31ea356761360e78d16525aa69d2dae6">   14</a></span>&#160;<span class="preprocessor">#define __DOC6(n1, n2, n3, n4, n5, n6) __doc_##n1##_##n2##_##n3##_##n4##_##n5##_##n6</span></div>
<div class="line"><a name="l00015"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a6f1e731f1c6d2893ff1bf98582a7de6e">   15</a></span>&#160;<span class="preprocessor">#define __DOC7(n1, n2, n3, n4, n5, n6, n7) \</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">    __doc_##n1##_##n2##_##n3##_##n4##_##n5##_##n6##_##n7</span></div>
<div class="line"><a name="l00017"></a><span class="lineno"><a class="line" href="pydoc__macros_8h.html#a4e578031ec998eaeb933d5caa6a7d28a">   17</a></span>&#160;<span class="preprocessor">#define DOC(...) __EXPAND(__EXPAND(__CAT2(__DOC, __VA_SIZE(__VA_ARGS__)))(__VA_ARGS__))</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160; </div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#endif </span><span class="comment">// PYDOC_MACROS_H</span></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_49e56c817e5e54854c35e136979f97ca.html">docs</a></li><li class="navelem"><a class="el" href="dir_359d2bec989c9a8deeeb9aee335c1c76.html">doxygen</a></li><li class="navelem"><a class="el" href="pydoc__macros_8h.html">pydoc_macros.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1 </li>
  </ul>
</div>
</body>
</html>
