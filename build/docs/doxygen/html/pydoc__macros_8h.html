<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>GNU Radio&#39;s SC_CODER Package: pydoc_macros.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">GNU Radio&#39;s SC_CODER Package
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('pydoc__macros_8h.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">pydoc_macros.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a href="pydoc__macros_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:afa7cf06549cf7fd295f622ef6f376357"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#afa7cf06549cf7fd295f622ef6f376357">__EXPAND</a>(x)&#160;&#160;&#160;x</td></tr>
<tr class="separator:afa7cf06549cf7fd295f622ef6f376357"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a251b23a2efed48ff2ba2d6f449ed33a3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a251b23a2efed48ff2ba2d6f449ed33a3">__COUNT</a>(_1,  _2,  _3,  _4,  _5,  _6,  _7,  COUNT, ...)&#160;&#160;&#160;COUNT</td></tr>
<tr class="separator:a251b23a2efed48ff2ba2d6f449ed33a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2815f7ab197591ba9559415c694f9ce0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a2815f7ab197591ba9559415c694f9ce0">__VA_SIZE</a>(...)&#160;&#160;&#160;<a class="el" href="pydoc__macros_8h.html#afa7cf06549cf7fd295f622ef6f376357">__EXPAND</a>(<a class="el" href="pydoc__macros_8h.html#a251b23a2efed48ff2ba2d6f449ed33a3">__COUNT</a>(__VA_ARGS__, 7, 6, 5, 4, 3, 2, 1))</td></tr>
<tr class="separator:a2815f7ab197591ba9559415c694f9ce0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afdd58e017733f2235c227ef8dd3b2de8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#afdd58e017733f2235c227ef8dd3b2de8">__CAT1</a>(a,  b)&#160;&#160;&#160;a##b</td></tr>
<tr class="separator:afdd58e017733f2235c227ef8dd3b2de8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af08fe30f2a41cef10658f6a892d20543"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#af08fe30f2a41cef10658f6a892d20543">__CAT2</a>(a,  b)&#160;&#160;&#160;<a class="el" href="pydoc__macros_8h.html#afdd58e017733f2235c227ef8dd3b2de8">__CAT1</a>(a, b)</td></tr>
<tr class="separator:af08fe30f2a41cef10658f6a892d20543"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43c147206df4346db5e9d6445aab28ae"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a43c147206df4346db5e9d6445aab28ae">__DOC1</a>(n1)&#160;&#160;&#160;__doc_##n1</td></tr>
<tr class="separator:a43c147206df4346db5e9d6445aab28ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53738db606593bc1554371d718695fed"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a53738db606593bc1554371d718695fed">__DOC2</a>(n1,  n2)&#160;&#160;&#160;__doc_##n1##_##n2</td></tr>
<tr class="separator:a53738db606593bc1554371d718695fed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a21baf08c04d32485764f227c32b5bf5d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a21baf08c04d32485764f227c32b5bf5d">__DOC3</a>(n1,  n2,  n3)&#160;&#160;&#160;__doc_##n1##_##n2##_##n3</td></tr>
<tr class="separator:a21baf08c04d32485764f227c32b5bf5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9463ba0ad86bcb4c59b5aecf8a37174c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a9463ba0ad86bcb4c59b5aecf8a37174c">__DOC4</a>(n1,  n2,  n3,  n4)&#160;&#160;&#160;__doc_##n1##_##n2##_##n3##_##n4</td></tr>
<tr class="separator:a9463ba0ad86bcb4c59b5aecf8a37174c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a942579d548421ddb2906af7094e5789c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a942579d548421ddb2906af7094e5789c">__DOC5</a>(n1,  n2,  n3,  n4,  n5)&#160;&#160;&#160;__doc_##n1##_##n2##_##n3##_##n4##_##n5</td></tr>
<tr class="separator:a942579d548421ddb2906af7094e5789c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31ea356761360e78d16525aa69d2dae6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a31ea356761360e78d16525aa69d2dae6">__DOC6</a>(n1,  n2,  n3,  n4,  n5,  n6)&#160;&#160;&#160;__doc_##n1##_##n2##_##n3##_##n4##_##n5##_##n6</td></tr>
<tr class="separator:a31ea356761360e78d16525aa69d2dae6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f1e731f1c6d2893ff1bf98582a7de6e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a6f1e731f1c6d2893ff1bf98582a7de6e">__DOC7</a>(n1,  n2,  n3,  n4,  n5,  n6,  n7)&#160;&#160;&#160;    __doc_##n1##_##n2##_##n3##_##n4##_##n5##_##n6##_##n7</td></tr>
<tr class="separator:a6f1e731f1c6d2893ff1bf98582a7de6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e578031ec998eaeb933d5caa6a7d28a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="pydoc__macros_8h.html#a4e578031ec998eaeb933d5caa6a7d28a">DOC</a>(...)&#160;&#160;&#160;<a class="el" href="pydoc__macros_8h.html#afa7cf06549cf7fd295f622ef6f376357">__EXPAND</a>(<a class="el" href="pydoc__macros_8h.html#afa7cf06549cf7fd295f622ef6f376357">__EXPAND</a>(<a class="el" href="pydoc__macros_8h.html#af08fe30f2a41cef10658f6a892d20543">__CAT2</a>(__DOC, <a class="el" href="pydoc__macros_8h.html#a2815f7ab197591ba9559415c694f9ce0">__VA_SIZE</a>(__VA_ARGS__)))(__VA_ARGS__))</td></tr>
<tr class="separator:a4e578031ec998eaeb933d5caa6a7d28a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="afdd58e017733f2235c227ef8dd3b2de8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afdd58e017733f2235c227ef8dd3b2de8">&#9670;&nbsp;</a></span>__CAT1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __CAT1</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">a, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">b&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;a##b</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af08fe30f2a41cef10658f6a892d20543"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af08fe30f2a41cef10658f6a892d20543">&#9670;&nbsp;</a></span>__CAT2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __CAT2</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">a, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">b&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;<a class="el" href="pydoc__macros_8h.html#afdd58e017733f2235c227ef8dd3b2de8">__CAT1</a>(a, b)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a251b23a2efed48ff2ba2d6f449ed33a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a251b23a2efed48ff2ba2d6f449ed33a3">&#9670;&nbsp;</a></span>__COUNT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __COUNT</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">_1, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">_2, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">_3, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">_4, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">_5, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">_6, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">_7, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">COUNT, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>...</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;COUNT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a43c147206df4346db5e9d6445aab28ae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a43c147206df4346db5e9d6445aab28ae">&#9670;&nbsp;</a></span>__DOC1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __DOC1</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n1</td><td>)</td>
          <td>&#160;&#160;&#160;__doc_##n1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a53738db606593bc1554371d718695fed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a53738db606593bc1554371d718695fed">&#9670;&nbsp;</a></span>__DOC2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __DOC2</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n1, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n2&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;__doc_##n1##_##n2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a21baf08c04d32485764f227c32b5bf5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21baf08c04d32485764f227c32b5bf5d">&#9670;&nbsp;</a></span>__DOC3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __DOC3</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n1, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n2, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n3&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;__doc_##n1##_##n2##_##n3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9463ba0ad86bcb4c59b5aecf8a37174c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9463ba0ad86bcb4c59b5aecf8a37174c">&#9670;&nbsp;</a></span>__DOC4</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __DOC4</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n1, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n2, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n3, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n4&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;__doc_##n1##_##n2##_##n3##_##n4</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a942579d548421ddb2906af7094e5789c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a942579d548421ddb2906af7094e5789c">&#9670;&nbsp;</a></span>__DOC5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __DOC5</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n1, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n2, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n3, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n4, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n5&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;__doc_##n1##_##n2##_##n3##_##n4##_##n5</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a31ea356761360e78d16525aa69d2dae6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a31ea356761360e78d16525aa69d2dae6">&#9670;&nbsp;</a></span>__DOC6</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __DOC6</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n1, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n2, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n3, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n4, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n5, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n6&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;__doc_##n1##_##n2##_##n3##_##n4##_##n5##_##n6</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6f1e731f1c6d2893ff1bf98582a7de6e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6f1e731f1c6d2893ff1bf98582a7de6e">&#9670;&nbsp;</a></span>__DOC7</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __DOC7</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n1, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n2, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n3, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n4, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n5, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n6, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">n7&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;    __doc_##n1##_##n2##_##n3##_##n4##_##n5##_##n6##_##n7</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="afa7cf06549cf7fd295f622ef6f376357"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa7cf06549cf7fd295f622ef6f376357">&#9670;&nbsp;</a></span>__EXPAND</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __EXPAND</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">x</td><td>)</td>
          <td>&#160;&#160;&#160;x</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2815f7ab197591ba9559415c694f9ce0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2815f7ab197591ba9559415c694f9ce0">&#9670;&nbsp;</a></span>__VA_SIZE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __VA_SIZE</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>...</em></td><td>)</td>
          <td>&#160;&#160;&#160;<a class="el" href="pydoc__macros_8h.html#afa7cf06549cf7fd295f622ef6f376357">__EXPAND</a>(<a class="el" href="pydoc__macros_8h.html#a251b23a2efed48ff2ba2d6f449ed33a3">__COUNT</a>(__VA_ARGS__, 7, 6, 5, 4, 3, 2, 1))</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4e578031ec998eaeb933d5caa6a7d28a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e578031ec998eaeb933d5caa6a7d28a">&#9670;&nbsp;</a></span>DOC</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define DOC</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>...</em></td><td>)</td>
          <td>&#160;&#160;&#160;<a class="el" href="pydoc__macros_8h.html#afa7cf06549cf7fd295f622ef6f376357">__EXPAND</a>(<a class="el" href="pydoc__macros_8h.html#afa7cf06549cf7fd295f622ef6f376357">__EXPAND</a>(<a class="el" href="pydoc__macros_8h.html#af08fe30f2a41cef10658f6a892d20543">__CAT2</a>(__DOC, <a class="el" href="pydoc__macros_8h.html#a2815f7ab197591ba9559415c694f9ce0">__VA_SIZE</a>(__VA_ARGS__)))(__VA_ARGS__))</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_49e56c817e5e54854c35e136979f97ca.html">docs</a></li><li class="navelem"><a class="el" href="dir_359d2bec989c9a8deeeb9aee335c1c76.html">doxygen</a></li><li class="navelem"><a class="el" href="pydoc__macros_8h.html">pydoc_macros.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1 </li>
  </ul>
</div>
</body>
</html>
