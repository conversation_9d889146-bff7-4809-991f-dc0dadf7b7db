<?xml version='1.0' encoding='utf-8' ?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import namespace="http://www.w3.org/XML/1998/namespace" schemaLocation="xml.xsd"/>

  <xsd:element name="doxygenindex" type="DoxygenType"/>

  <xsd:complexType name="DoxygenType">
    <xsd:sequence>
      <xsd:element name="compound" type="CompoundType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="version" type="xsd:string" use="required"/>
    <xsd:attribute ref="xml:lang" use="required"/>
  </xsd:complexType>

  <xsd:complexType name="CompoundType">
    <xsd:sequence>
      <xsd:element name="name" type="xsd:string"/>
      <xsd:element name="member" type="MemberType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="refid" type="xsd:string" use="required"/>
    <xsd:attribute name="kind" type="CompoundKind" use="required"/>
  </xsd:complexType>

  <xsd:complexType name="MemberType">
    <xsd:sequence>
      <xsd:element name="name" type="xsd:string"/>
    </xsd:sequence>
    <xsd:attribute name="refid" type="xsd:string" use="required"/>
    <xsd:attribute name="kind" type="MemberKind" use="required"/>
  </xsd:complexType>
  
  <xsd:simpleType name="CompoundKind">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="class"/>
      <xsd:enumeration value="struct"/>
      <xsd:enumeration value="union"/>
      <xsd:enumeration value="interface"/>
      <xsd:enumeration value="protocol"/>
      <xsd:enumeration value="category"/>
      <xsd:enumeration value="exception"/>
      <xsd:enumeration value="file"/>
      <xsd:enumeration value="namespace"/>
      <xsd:enumeration value="group"/>
      <xsd:enumeration value="page"/>
      <xsd:enumeration value="example"/>
      <xsd:enumeration value="dir"/>
      <xsd:enumeration value="type"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="MemberKind">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="define"/>
      <xsd:enumeration value="property"/>
      <xsd:enumeration value="event"/>
      <xsd:enumeration value="variable"/>
      <xsd:enumeration value="typedef"/>
      <xsd:enumeration value="enum"/>
      <xsd:enumeration value="enumvalue"/>
      <xsd:enumeration value="function"/>
      <xsd:enumeration value="signal"/>
      <xsd:enumeration value="prototype"/>
      <xsd:enumeration value="friend"/>
      <xsd:enumeration value="dcop"/>
      <xsd:enumeration value="slot"/>
    </xsd:restriction>
  </xsd:simpleType>

</xsd:schema>

