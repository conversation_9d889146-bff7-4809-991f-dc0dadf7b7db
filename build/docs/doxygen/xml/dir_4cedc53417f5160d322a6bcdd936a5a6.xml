<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.9.1" xml:lang="en-US">
  <compounddef id="dir_4cedc53417f5160d322a6bcdd936a5a6" kind="dir">
    <compoundname>sc_coder</compoundname>
    <innerfile refid="api_8h">api.h</innerfile>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <location file="/home/<USER>/gr-sc_coder/include/gnuradio/sc_coder/"/>
  </compounddef>
</doxygen>
