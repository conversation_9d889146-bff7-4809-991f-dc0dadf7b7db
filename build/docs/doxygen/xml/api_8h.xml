<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.9.1" xml:lang="en-US">
  <compounddef id="api_8h" kind="file" language="C++">
    <compoundname>api.h</compoundname>
    <includes local="no">gnuradio/attributes.h</includes>
    <incdepgraph>
      <node id="2">
        <label>gnuradio/attributes.h</label>
      </node>
      <node id="1">
        <label>api.h</label>
        <link refid="api_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
    </incdepgraph>
      <sectiondef kind="define">
      <memberdef kind="define" id="api_8h_1ae7a545e1952315e2d35103b6927a4b06" prot="public" static="no">
        <name>SC_CODER_API</name>
        <initializer>__GR_ATTR_IMPORT</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/include/gnuradio/sc_coder/api.h" line="19" column="10" bodyfile="/home/<USER>/gr-sc_coder/include/gnuradio/sc_coder/api.h" bodystart="19" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <location file="/home/<USER>/gr-sc_coder/include/gnuradio/sc_coder/api.h"/>
  </compounddef>
</doxygen>
