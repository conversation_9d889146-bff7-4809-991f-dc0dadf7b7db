<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.9.1" xml:lang="en-US">
  <compounddef id="namespacestd" kind="namespace" language="Unknown">
    <compoundname>std</compoundname>
    <innerclass refid="classstd_1_1allocator" prot="public">std::allocator</innerclass>
    <innerclass refid="classstd_1_1auto__ptr" prot="public">std::auto_ptr</innerclass>
    <innerclass refid="classstd_1_1smart__ptr" prot="public">std::smart_ptr</innerclass>
    <innerclass refid="classstd_1_1unique__ptr" prot="public">std::unique_ptr</innerclass>
    <innerclass refid="classstd_1_1shared__ptr" prot="public">std::shared_ptr</innerclass>
    <innerclass refid="classstd_1_1weak__ptr" prot="public">std::weak_ptr</innerclass>
    <innerclass refid="classstd_1_1atomic" prot="public">std::atomic</innerclass>
    <innerclass refid="classstd_1_1atomic__ref" prot="public">std::atomic_ref</innerclass>
    <innerclass refid="classstd_1_1lock__guard" prot="public">std::lock_guard</innerclass>
    <innerclass refid="classstd_1_1unique__lock" prot="public">std::unique_lock</innerclass>
    <innerclass refid="classstd_1_1shared__lock" prot="public">std::shared_lock</innerclass>
    <innerclass refid="classstd_1_1ios__base" prot="public">std::ios_base</innerclass>
    <innerclass refid="classstd_1_1error__code" prot="public">std::error_code</innerclass>
    <innerclass refid="classstd_1_1error__category" prot="public">std::error_category</innerclass>
    <innerclass refid="classstd_1_1system__error" prot="public">std::system_error</innerclass>
    <innerclass refid="classstd_1_1error__condition" prot="public">std::error_condition</innerclass>
    <innerclass refid="classstd_1_1thread" prot="public">std::thread</innerclass>
    <innerclass refid="classstd_1_1jthread" prot="public">std::jthread</innerclass>
    <innerclass refid="classstd_1_1mutex" prot="public">std::mutex</innerclass>
    <innerclass refid="classstd_1_1timed__mutex" prot="public">std::timed_mutex</innerclass>
    <innerclass refid="classstd_1_1recursive__mutex" prot="public">std::recursive_mutex</innerclass>
    <innerclass refid="classstd_1_1recursive__timed__mutex" prot="public">std::recursive_timed_mutex</innerclass>
    <innerclass refid="classstd_1_1shared__mutex" prot="public">std::shared_mutex</innerclass>
    <innerclass refid="classstd_1_1shared__timed__mutex" prot="public">std::shared_timed_mutex</innerclass>
    <innerclass refid="classstd_1_1basic__ios" prot="public">std::basic_ios</innerclass>
    <innerclass refid="classstd_1_1basic__istream" prot="public">std::basic_istream</innerclass>
    <innerclass refid="classstd_1_1basic__ostream" prot="public">std::basic_ostream</innerclass>
    <innerclass refid="classstd_1_1basic__iostream" prot="public">std::basic_iostream</innerclass>
    <innerclass refid="classstd_1_1basic__ifstream" prot="public">std::basic_ifstream</innerclass>
    <innerclass refid="classstd_1_1basic__ofstream" prot="public">std::basic_ofstream</innerclass>
    <innerclass refid="classstd_1_1basic__fstream" prot="public">std::basic_fstream</innerclass>
    <innerclass refid="classstd_1_1basic__istringstream" prot="public">std::basic_istringstream</innerclass>
    <innerclass refid="classstd_1_1basic__ostringstream" prot="public">std::basic_ostringstream</innerclass>
    <innerclass refid="classstd_1_1basic__stringstream" prot="public">std::basic_stringstream</innerclass>
    <innerclass refid="classstd_1_1ios" prot="public">std::ios</innerclass>
    <innerclass refid="classstd_1_1wios" prot="public">std::wios</innerclass>
    <innerclass refid="classstd_1_1istream" prot="public">std::istream</innerclass>
    <innerclass refid="classstd_1_1wistream" prot="public">std::wistream</innerclass>
    <innerclass refid="classstd_1_1ostream" prot="public">std::ostream</innerclass>
    <innerclass refid="classstd_1_1wostream" prot="public">std::wostream</innerclass>
    <innerclass refid="classstd_1_1ifstream" prot="public">std::ifstream</innerclass>
    <innerclass refid="classstd_1_1wifstream" prot="public">std::wifstream</innerclass>
    <innerclass refid="classstd_1_1ofstream" prot="public">std::ofstream</innerclass>
    <innerclass refid="classstd_1_1wofstream" prot="public">std::wofstream</innerclass>
    <innerclass refid="classstd_1_1fstream" prot="public">std::fstream</innerclass>
    <innerclass refid="classstd_1_1wfstream" prot="public">std::wfstream</innerclass>
    <innerclass refid="classstd_1_1istringstream" prot="public">std::istringstream</innerclass>
    <innerclass refid="classstd_1_1wistringstream" prot="public">std::wistringstream</innerclass>
    <innerclass refid="classstd_1_1ostringstream" prot="public">std::ostringstream</innerclass>
    <innerclass refid="classstd_1_1wostringstream" prot="public">std::wostringstream</innerclass>
    <innerclass refid="classstd_1_1stringstream" prot="public">std::stringstream</innerclass>
    <innerclass refid="classstd_1_1wstringstream" prot="public">std::wstringstream</innerclass>
    <innerclass refid="classstd_1_1basic__string" prot="public">std::basic_string</innerclass>
    <innerclass refid="classstd_1_1string" prot="public">std::string</innerclass>
    <innerclass refid="classstd_1_1wstring" prot="public">std::wstring</innerclass>
    <innerclass refid="classstd_1_1u8string" prot="public">std::u8string</innerclass>
    <innerclass refid="classstd_1_1u16string" prot="public">std::u16string</innerclass>
    <innerclass refid="classstd_1_1u32string" prot="public">std::u32string</innerclass>
    <innerclass refid="classstd_1_1basic__string__view" prot="public">std::basic_string_view</innerclass>
    <innerclass refid="classstd_1_1string__view" prot="public">std::string_view</innerclass>
    <innerclass refid="classstd_1_1wstring__view" prot="public">std::wstring_view</innerclass>
    <innerclass refid="classstd_1_1u8string__view" prot="public">std::u8string_view</innerclass>
    <innerclass refid="classstd_1_1u16string__view" prot="public">std::u16string_view</innerclass>
    <innerclass refid="classstd_1_1u32string__view" prot="public">std::u32string_view</innerclass>
    <innerclass refid="classstd_1_1complex" prot="public">std::complex</innerclass>
    <innerclass refid="classstd_1_1bitset" prot="public">std::bitset</innerclass>
    <innerclass refid="classstd_1_1deque" prot="public">std::deque</innerclass>
    <innerclass refid="classstd_1_1list" prot="public">std::list</innerclass>
    <innerclass refid="classstd_1_1forward__list" prot="public">std::forward_list</innerclass>
    <innerclass refid="classstd_1_1map" prot="public">std::map</innerclass>
    <innerclass refid="classstd_1_1unordered__map" prot="public">std::unordered_map</innerclass>
    <innerclass refid="classstd_1_1multimap" prot="public">std::multimap</innerclass>
    <innerclass refid="classstd_1_1unordered__multimap" prot="public">std::unordered_multimap</innerclass>
    <innerclass refid="classstd_1_1set" prot="public">std::set</innerclass>
    <innerclass refid="classstd_1_1unordered__set" prot="public">std::unordered_set</innerclass>
    <innerclass refid="classstd_1_1multiset" prot="public">std::multiset</innerclass>
    <innerclass refid="classstd_1_1unordered__multiset" prot="public">std::unordered_multiset</innerclass>
    <innerclass refid="classstd_1_1array" prot="public">std::array</innerclass>
    <innerclass refid="classstd_1_1vector" prot="public">std::vector</innerclass>
    <innerclass refid="classstd_1_1span" prot="public">std::span</innerclass>
    <innerclass refid="classstd_1_1queue" prot="public">std::queue</innerclass>
    <innerclass refid="classstd_1_1priority__queue" prot="public">std::priority_queue</innerclass>
    <innerclass refid="classstd_1_1stack" prot="public">std::stack</innerclass>
    <innerclass refid="classstd_1_1valarray" prot="public">std::valarray</innerclass>
    <innerclass refid="classstd_1_1exception" prot="public">std::exception</innerclass>
    <innerclass refid="classstd_1_1bad__alloc" prot="public">std::bad_alloc</innerclass>
    <innerclass refid="classstd_1_1bad__cast" prot="public">std::bad_cast</innerclass>
    <innerclass refid="classstd_1_1bad__typeid" prot="public">std::bad_typeid</innerclass>
    <innerclass refid="classstd_1_1logic__error" prot="public">std::logic_error</innerclass>
    <innerclass refid="classstd_1_1runtime__error" prot="public">std::runtime_error</innerclass>
    <innerclass refid="classstd_1_1bad__exception" prot="public">std::bad_exception</innerclass>
    <innerclass refid="classstd_1_1domain__error" prot="public">std::domain_error</innerclass>
    <innerclass refid="classstd_1_1invalid__argument" prot="public">std::invalid_argument</innerclass>
    <innerclass refid="classstd_1_1length__error" prot="public">std::length_error</innerclass>
    <innerclass refid="classstd_1_1out__of__range" prot="public">std::out_of_range</innerclass>
    <innerclass refid="classstd_1_1range__error" prot="public">std::range_error</innerclass>
    <innerclass refid="classstd_1_1overflow__error" prot="public">std::overflow_error</innerclass>
    <innerclass refid="classstd_1_1underflow__error" prot="public">std::underflow_error</innerclass>
    <briefdescription>
<para>STL namespace. </para>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <location file="[STL]" line="1" column="1"/>
  </compounddef>
</doxygen>
