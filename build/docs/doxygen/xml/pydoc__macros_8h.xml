<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.9.1" xml:lang="en-US">
  <compounddef id="pydoc__macros_8h" kind="file" language="C++">
    <compoundname>pydoc_macros.h</compoundname>
      <sectiondef kind="define">
      <memberdef kind="define" id="pydoc__macros_8h_1afa7cf06549cf7fd295f622ef6f376357" prot="public" static="no">
        <name>__EXPAND</name>
        <param><defname>x</defname></param>
        <initializer>x</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="4" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="4" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a251b23a2efed48ff2ba2d6f449ed33a3" prot="public" static="no">
        <name>__COUNT</name>
        <param><defname>_1</defname></param>
        <param><defname>_2</defname></param>
        <param><defname>_3</defname></param>
        <param><defname>_4</defname></param>
        <param><defname>_5</defname></param>
        <param><defname>_6</defname></param>
        <param><defname>_7</defname></param>
        <param><defname>COUNT</defname></param>
        <param><defname>...</defname></param>
        <initializer>COUNT</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="5" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="5" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a2815f7ab197591ba9559415c694f9ce0" prot="public" static="no">
        <name>__VA_SIZE</name>
        <param><defname>...</defname></param>
        <initializer><ref refid="pydoc__macros_8h_1afa7cf06549cf7fd295f622ef6f376357" kindref="member">__EXPAND</ref>(<ref refid="pydoc__macros_8h_1a251b23a2efed48ff2ba2d6f449ed33a3" kindref="member">__COUNT</ref>(__VA_ARGS__, 7, 6, 5, 4, 3, 2, 1))</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="6" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="6" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1afdd58e017733f2235c227ef8dd3b2de8" prot="public" static="no">
        <name>__CAT1</name>
        <param><defname>a</defname></param>
        <param><defname>b</defname></param>
        <initializer>a##b</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="7" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="7" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1af08fe30f2a41cef10658f6a892d20543" prot="public" static="no">
        <name>__CAT2</name>
        <param><defname>a</defname></param>
        <param><defname>b</defname></param>
        <initializer><ref refid="pydoc__macros_8h_1afdd58e017733f2235c227ef8dd3b2de8" kindref="member">__CAT1</ref>(a, b)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="8" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="8" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a43c147206df4346db5e9d6445aab28ae" prot="public" static="no">
        <name>__DOC1</name>
        <param><defname>n1</defname></param>
        <initializer>__doc_##n1</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="9" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="9" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a53738db606593bc1554371d718695fed" prot="public" static="no">
        <name>__DOC2</name>
        <param><defname>n1</defname></param>
        <param><defname>n2</defname></param>
        <initializer>__doc_##n1##_##n2</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="10" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="10" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a21baf08c04d32485764f227c32b5bf5d" prot="public" static="no">
        <name>__DOC3</name>
        <param><defname>n1</defname></param>
        <param><defname>n2</defname></param>
        <param><defname>n3</defname></param>
        <initializer>__doc_##n1##_##n2##_##n3</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="11" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="11" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a9463ba0ad86bcb4c59b5aecf8a37174c" prot="public" static="no">
        <name>__DOC4</name>
        <param><defname>n1</defname></param>
        <param><defname>n2</defname></param>
        <param><defname>n3</defname></param>
        <param><defname>n4</defname></param>
        <initializer>__doc_##n1##_##n2##_##n3##_##n4</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="12" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="12" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a942579d548421ddb2906af7094e5789c" prot="public" static="no">
        <name>__DOC5</name>
        <param><defname>n1</defname></param>
        <param><defname>n2</defname></param>
        <param><defname>n3</defname></param>
        <param><defname>n4</defname></param>
        <param><defname>n5</defname></param>
        <initializer>__doc_##n1##_##n2##_##n3##_##n4##_##n5</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="13" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="13" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a31ea356761360e78d16525aa69d2dae6" prot="public" static="no">
        <name>__DOC6</name>
        <param><defname>n1</defname></param>
        <param><defname>n2</defname></param>
        <param><defname>n3</defname></param>
        <param><defname>n4</defname></param>
        <param><defname>n5</defname></param>
        <param><defname>n6</defname></param>
        <initializer>__doc_##n1##_##n2##_##n3##_##n4##_##n5##_##n6</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="14" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="14" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a6f1e731f1c6d2893ff1bf98582a7de6e" prot="public" static="no">
        <name>__DOC7</name>
        <param><defname>n1</defname></param>
        <param><defname>n2</defname></param>
        <param><defname>n3</defname></param>
        <param><defname>n4</defname></param>
        <param><defname>n5</defname></param>
        <param><defname>n6</defname></param>
        <param><defname>n7</defname></param>
        <initializer>    __doc_##n1##_##n2##_##n3##_##n4##_##n5##_##n6##_##n7</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="15" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="15" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="pydoc__macros_8h_1a4e578031ec998eaeb933d5caa6a7d28a" prot="public" static="no">
        <name>DOC</name>
        <param><defname>...</defname></param>
        <initializer><ref refid="pydoc__macros_8h_1afa7cf06549cf7fd295f622ef6f376357" kindref="member">__EXPAND</ref>(<ref refid="pydoc__macros_8h_1afa7cf06549cf7fd295f622ef6f376357" kindref="member">__EXPAND</ref>(<ref refid="pydoc__macros_8h_1af08fe30f2a41cef10658f6a892d20543" kindref="member">__CAT2</ref>(__DOC, <ref refid="pydoc__macros_8h_1a2815f7ab197591ba9559415c694f9ce0" kindref="member">__VA_SIZE</ref>(__VA_ARGS__)))(__VA_ARGS__))</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" line="17" column="9" bodyfile="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h" bodystart="17" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <location file="/home/<USER>/gr-sc_coder/docs/doxygen/pydoc_macros.h"/>
  </compounddef>
</doxygen>
