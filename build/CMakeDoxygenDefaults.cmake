#
# DO NOT EDIT! THIS FILE WAS GENERATED BY CMAKE!
#

if(NOT DEFINED DOXYGEN_DOXYFILE_ENCODING)
    set(DOXYGEN_DOXYFILE_ENCODING UTF-8)
endif()
if(NOT DEFINED DOXYGEN_PROJECT_NAME)
    set(D<PERSON>YGEN_PROJECT_NAME "My Project")
endif()
if(NOT DEFINED DOXYGEN_CREATE_SUBDIRS)
    set(DOXYGEN_CREATE_SUBDIRS NO)
endif()
if(NOT DEFINED DOXYGEN_ALLOW_UNICODE_NAMES)
    set(DOXYGEN_ALLOW_UNICODE_NAMES NO)
endif()
if(NOT DEFINED DOXYGEN_OUTPUT_LANGUAGE)
    set(DOXYGEN_OUTPUT_LANGUAGE English)
endif()
if(NOT DEFINED DOXYGEN_OUTPUT_TEXT_DIRECTION)
    set(DOXYGEN_OUTPUT_TEXT_DIRECTION None)
endif()
if(NOT DEFINED DOXYGEN_BRIEF_MEMBER_DESC)
    set(DOXYGEN_BRIEF_MEMBER_DESC YES)
endif()
if(NOT DEFINED DOXYGEN_REPEAT_BRIEF)
    set(DOXYGEN_REPEAT_BRIEF YES)
endif()
if(NOT DEFINED DOXYGEN_ABBREVIATE_BRIEF)
    set(DOXYGEN_ABBREVIATE_BRIEF "The $name class" 
                         "The $name widget" 
                         "The $name file" 
                         is 
                         provides 
                         specifies 
                         contains 
                         represents 
                         a 
                         an 
                         the)
endif()
if(NOT DEFINED DOXYGEN_ALWAYS_DETAILED_SEC)
    set(DOXYGEN_ALWAYS_DETAILED_SEC NO)
endif()
if(NOT DEFINED DOXYGEN_INLINE_INHERITED_MEMB)
    set(DOXYGEN_INLINE_INHERITED_MEMB NO)
endif()
if(NOT DEFINED DOXYGEN_FULL_PATH_NAMES)
    set(DOXYGEN_FULL_PATH_NAMES YES)
endif()
if(NOT DEFINED DOXYGEN_SHORT_NAMES)
    set(DOXYGEN_SHORT_NAMES NO)
endif()
if(NOT DEFINED DOXYGEN_JAVADOC_AUTOBRIEF)
    set(DOXYGEN_JAVADOC_AUTOBRIEF NO)
endif()
if(NOT DEFINED DOXYGEN_JAVADOC_BANNER)
    set(DOXYGEN_JAVADOC_BANNER NO)
endif()
if(NOT DEFINED DOXYGEN_QT_AUTOBRIEF)
    set(DOXYGEN_QT_AUTOBRIEF NO)
endif()
if(NOT DEFINED DOXYGEN_MULTILINE_CPP_IS_BRIEF)
    set(DOXYGEN_MULTILINE_CPP_IS_BRIEF NO)
endif()
if(NOT DEFINED DOXYGEN_PYTHON_DOCSTRING)
    set(DOXYGEN_PYTHON_DOCSTRING YES)
endif()
if(NOT DEFINED DOXYGEN_INHERIT_DOCS)
    set(DOXYGEN_INHERIT_DOCS YES)
endif()
if(NOT DEFINED DOXYGEN_SEPARATE_MEMBER_PAGES)
    set(DOXYGEN_SEPARATE_MEMBER_PAGES NO)
endif()
if(NOT DEFINED DOXYGEN_TAB_SIZE)
    set(DOXYGEN_TAB_SIZE 4)
endif()
if(NOT DEFINED DOXYGEN_OPTIMIZE_OUTPUT_FOR_C)
    set(DOXYGEN_OPTIMIZE_OUTPUT_FOR_C NO)
endif()
if(NOT DEFINED DOXYGEN_OPTIMIZE_OUTPUT_JAVA)
    set(DOXYGEN_OPTIMIZE_OUTPUT_JAVA NO)
endif()
if(NOT DEFINED DOXYGEN_OPTIMIZE_FOR_FORTRAN)
    set(DOXYGEN_OPTIMIZE_FOR_FORTRAN NO)
endif()
if(NOT DEFINED DOXYGEN_OPTIMIZE_OUTPUT_VHDL)
    set(DOXYGEN_OPTIMIZE_OUTPUT_VHDL NO)
endif()
if(NOT DEFINED DOXYGEN_OPTIMIZE_OUTPUT_SLICE)
    set(DOXYGEN_OPTIMIZE_OUTPUT_SLICE NO)
endif()
if(NOT DEFINED DOXYGEN_MARKDOWN_SUPPORT)
    set(DOXYGEN_MARKDOWN_SUPPORT YES)
endif()
if(NOT DEFINED DOXYGEN_TOC_INCLUDE_HEADINGS)
    set(DOXYGEN_TOC_INCLUDE_HEADINGS 5)
endif()
if(NOT DEFINED DOXYGEN_AUTOLINK_SUPPORT)
    set(DOXYGEN_AUTOLINK_SUPPORT YES)
endif()
if(NOT DEFINED DOXYGEN_BUILTIN_STL_SUPPORT)
    set(DOXYGEN_BUILTIN_STL_SUPPORT NO)
endif()
if(NOT DEFINED DOXYGEN_CPP_CLI_SUPPORT)
    set(DOXYGEN_CPP_CLI_SUPPORT NO)
endif()
if(NOT DEFINED DOXYGEN_SIP_SUPPORT)
    set(DOXYGEN_SIP_SUPPORT NO)
endif()
if(NOT DEFINED DOXYGEN_IDL_PROPERTY_SUPPORT)
    set(DOXYGEN_IDL_PROPERTY_SUPPORT YES)
endif()
if(NOT DEFINED DOXYGEN_DISTRIBUTE_GROUP_DOC)
    set(DOXYGEN_DISTRIBUTE_GROUP_DOC NO)
endif()
if(NOT DEFINED DOXYGEN_GROUP_NESTED_COMPOUNDS)
    set(DOXYGEN_GROUP_NESTED_COMPOUNDS NO)
endif()
if(NOT DEFINED DOXYGEN_SUBGROUPING)
    set(DOXYGEN_SUBGROUPING YES)
endif()
if(NOT DEFINED DOXYGEN_INLINE_GROUPED_CLASSES)
    set(DOXYGEN_INLINE_GROUPED_CLASSES NO)
endif()
if(NOT DEFINED DOXYGEN_INLINE_SIMPLE_STRUCTS)
    set(DOXYGEN_INLINE_SIMPLE_STRUCTS NO)
endif()
if(NOT DEFINED DOXYGEN_TYPEDEF_HIDES_STRUCT)
    set(DOXYGEN_TYPEDEF_HIDES_STRUCT NO)
endif()
if(NOT DEFINED DOXYGEN_LOOKUP_CACHE_SIZE)
    set(DOXYGEN_LOOKUP_CACHE_SIZE 0)
endif()
if(NOT DEFINED DOXYGEN_NUM_PROC_THREADS)
    set(DOXYGEN_NUM_PROC_THREADS 1)
endif()
if(NOT DEFINED DOXYGEN_EXTRACT_ALL)
    set(DOXYGEN_EXTRACT_ALL NO)
endif()
if(NOT DEFINED DOXYGEN_EXTRACT_PRIVATE)
    set(DOXYGEN_EXTRACT_PRIVATE NO)
endif()
if(NOT DEFINED DOXYGEN_EXTRACT_PRIV_VIRTUAL)
    set(DOXYGEN_EXTRACT_PRIV_VIRTUAL NO)
endif()
if(NOT DEFINED DOXYGEN_EXTRACT_PACKAGE)
    set(DOXYGEN_EXTRACT_PACKAGE NO)
endif()
if(NOT DEFINED DOXYGEN_EXTRACT_STATIC)
    set(DOXYGEN_EXTRACT_STATIC NO)
endif()
if(NOT DEFINED DOXYGEN_EXTRACT_LOCAL_CLASSES)
    set(DOXYGEN_EXTRACT_LOCAL_CLASSES YES)
endif()
if(NOT DEFINED DOXYGEN_EXTRACT_LOCAL_METHODS)
    set(DOXYGEN_EXTRACT_LOCAL_METHODS NO)
endif()
if(NOT DEFINED DOXYGEN_EXTRACT_ANON_NSPACES)
    set(DOXYGEN_EXTRACT_ANON_NSPACES NO)
endif()
if(NOT DEFINED DOXYGEN_RESOLVE_UNNAMED_PARAMS)
    set(DOXYGEN_RESOLVE_UNNAMED_PARAMS YES)
endif()
if(NOT DEFINED DOXYGEN_HIDE_UNDOC_MEMBERS)
    set(DOXYGEN_HIDE_UNDOC_MEMBERS NO)
endif()
if(NOT DEFINED DOXYGEN_HIDE_UNDOC_CLASSES)
    set(DOXYGEN_HIDE_UNDOC_CLASSES NO)
endif()
if(NOT DEFINED DOXYGEN_HIDE_FRIEND_COMPOUNDS)
    set(DOXYGEN_HIDE_FRIEND_COMPOUNDS NO)
endif()
if(NOT DEFINED DOXYGEN_HIDE_IN_BODY_DOCS)
    set(DOXYGEN_HIDE_IN_BODY_DOCS NO)
endif()
if(NOT DEFINED DOXYGEN_INTERNAL_DOCS)
    set(DOXYGEN_INTERNAL_DOCS NO)
endif()
if(NOT DEFINED DOXYGEN_CASE_SENSE_NAMES)
    set(DOXYGEN_CASE_SENSE_NAMES YES)
endif()
if(NOT DEFINED DOXYGEN_HIDE_SCOPE_NAMES)
    set(DOXYGEN_HIDE_SCOPE_NAMES NO)
endif()
if(NOT DEFINED DOXYGEN_HIDE_COMPOUND_REFERENCE)
    set(DOXYGEN_HIDE_COMPOUND_REFERENCE NO)
endif()
if(NOT DEFINED DOXYGEN_SHOW_INCLUDE_FILES)
    set(DOXYGEN_SHOW_INCLUDE_FILES YES)
endif()
if(NOT DEFINED DOXYGEN_SHOW_GROUPED_MEMB_INC)
    set(DOXYGEN_SHOW_GROUPED_MEMB_INC NO)
endif()
if(NOT DEFINED DOXYGEN_FORCE_LOCAL_INCLUDES)
    set(DOXYGEN_FORCE_LOCAL_INCLUDES NO)
endif()
if(NOT DEFINED DOXYGEN_INLINE_INFO)
    set(DOXYGEN_INLINE_INFO YES)
endif()
if(NOT DEFINED DOXYGEN_SORT_MEMBER_DOCS)
    set(DOXYGEN_SORT_MEMBER_DOCS YES)
endif()
if(NOT DEFINED DOXYGEN_SORT_BRIEF_DOCS)
    set(DOXYGEN_SORT_BRIEF_DOCS NO)
endif()
if(NOT DEFINED DOXYGEN_SORT_MEMBERS_CTORS_1ST)
    set(DOXYGEN_SORT_MEMBERS_CTORS_1ST NO)
endif()
if(NOT DEFINED DOXYGEN_SORT_GROUP_NAMES)
    set(DOXYGEN_SORT_GROUP_NAMES NO)
endif()
if(NOT DEFINED DOXYGEN_SORT_BY_SCOPE_NAME)
    set(DOXYGEN_SORT_BY_SCOPE_NAME NO)
endif()
if(NOT DEFINED DOXYGEN_STRICT_PROTO_MATCHING)
    set(DOXYGEN_STRICT_PROTO_MATCHING NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_TODOLIST)
    set(DOXYGEN_GENERATE_TODOLIST YES)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_TESTLIST)
    set(DOXYGEN_GENERATE_TESTLIST YES)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_BUGLIST)
    set(DOXYGEN_GENERATE_BUGLIST YES)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_DEPRECATEDLIST)
    set(DOXYGEN_GENERATE_DEPRECATEDLIST YES)
endif()
if(NOT DEFINED DOXYGEN_MAX_INITIALIZER_LINES)
    set(DOXYGEN_MAX_INITIALIZER_LINES 30)
endif()
if(NOT DEFINED DOXYGEN_SHOW_USED_FILES)
    set(DOXYGEN_SHOW_USED_FILES YES)
endif()
if(NOT DEFINED DOXYGEN_SHOW_FILES)
    set(DOXYGEN_SHOW_FILES YES)
endif()
if(NOT DEFINED DOXYGEN_SHOW_NAMESPACES)
    set(DOXYGEN_SHOW_NAMESPACES YES)
endif()
if(NOT DEFINED DOXYGEN_QUIET)
    set(DOXYGEN_QUIET NO)
endif()
if(NOT DEFINED DOXYGEN_WARNINGS)
    set(DOXYGEN_WARNINGS YES)
endif()
if(NOT DEFINED DOXYGEN_WARN_IF_UNDOCUMENTED)
    set(DOXYGEN_WARN_IF_UNDOCUMENTED YES)
endif()
if(NOT DEFINED DOXYGEN_WARN_IF_DOC_ERROR)
    set(DOXYGEN_WARN_IF_DOC_ERROR YES)
endif()
if(NOT DEFINED DOXYGEN_WARN_NO_PARAMDOC)
    set(DOXYGEN_WARN_NO_PARAMDOC NO)
endif()
if(NOT DEFINED DOXYGEN_WARN_AS_ERROR)
    set(DOXYGEN_WARN_AS_ERROR NO)
endif()
if(NOT DEFINED DOXYGEN_WARN_FORMAT)
    set(DOXYGEN_WARN_FORMAT "$file:$line: $text")
endif()
if(NOT DEFINED DOXYGEN_INPUT_ENCODING)
    set(DOXYGEN_INPUT_ENCODING UTF-8)
endif()
if(NOT DEFINED DOXYGEN_FILE_PATTERNS)
    set(DOXYGEN_FILE_PATTERNS *.c 
                         *.cc 
                         *.cxx 
                         *.cpp 
                         *.c++ 
                         *.java 
                         *.ii 
                         *.ixx 
                         *.ipp 
                         *.i++ 
                         *.inl 
                         *.idl 
                         *.ddl 
                         *.odl 
                         *.h 
                         *.hh 
                         *.hxx 
                         *.hpp 
                         *.h++ 
                         *.cs 
                         *.d 
                         *.php 
                         *.php4 
                         *.php5 
                         *.phtml 
                         *.inc 
                         *.m 
                         *.markdown 
                         *.md 
                         *.mm 
                         *.dox 
                         *.py 
                         *.pyw 
                         *.f90 
                         *.f95 
                         *.f03 
                         *.f08 
                         *.f18 
                         *.f 
                         *.for 
                         *.vhd 
                         *.vhdl 
                         *.ucf 
                         *.qsf 
                         *.ice)
endif()
if(NOT DEFINED DOXYGEN_RECURSIVE)
    set(DOXYGEN_RECURSIVE NO)
endif()
if(NOT DEFINED DOXYGEN_EXCLUDE_SYMLINKS)
    set(DOXYGEN_EXCLUDE_SYMLINKS NO)
endif()
if(NOT DEFINED DOXYGEN_EXAMPLE_PATTERNS)
    set(DOXYGEN_EXAMPLE_PATTERNS *)
endif()
if(NOT DEFINED DOXYGEN_EXAMPLE_RECURSIVE)
    set(DOXYGEN_EXAMPLE_RECURSIVE NO)
endif()
if(NOT DEFINED DOXYGEN_FILTER_SOURCE_FILES)
    set(DOXYGEN_FILTER_SOURCE_FILES NO)
endif()
if(NOT DEFINED DOXYGEN_SOURCE_BROWSER)
    set(DOXYGEN_SOURCE_BROWSER NO)
endif()
if(NOT DEFINED DOXYGEN_INLINE_SOURCES)
    set(DOXYGEN_INLINE_SOURCES NO)
endif()
if(NOT DEFINED DOXYGEN_STRIP_CODE_COMMENTS)
    set(DOXYGEN_STRIP_CODE_COMMENTS YES)
endif()
if(NOT DEFINED DOXYGEN_REFERENCED_BY_RELATION)
    set(DOXYGEN_REFERENCED_BY_RELATION NO)
endif()
if(NOT DEFINED DOXYGEN_REFERENCES_RELATION)
    set(DOXYGEN_REFERENCES_RELATION NO)
endif()
if(NOT DEFINED DOXYGEN_REFERENCES_LINK_SOURCE)
    set(DOXYGEN_REFERENCES_LINK_SOURCE YES)
endif()
if(NOT DEFINED DOXYGEN_SOURCE_TOOLTIPS)
    set(DOXYGEN_SOURCE_TOOLTIPS YES)
endif()
if(NOT DEFINED DOXYGEN_USE_HTAGS)
    set(DOXYGEN_USE_HTAGS NO)
endif()
if(NOT DEFINED DOXYGEN_VERBATIM_HEADERS)
    set(DOXYGEN_VERBATIM_HEADERS YES)
endif()
if(NOT DEFINED DOXYGEN_CLANG_ASSISTED_PARSING)
    set(DOXYGEN_CLANG_ASSISTED_PARSING NO)
endif()
if(NOT DEFINED DOXYGEN_CLANG_ADD_INC_PATHS)
    set(DOXYGEN_CLANG_ADD_INC_PATHS YES)
endif()
if(NOT DEFINED DOXYGEN_ALPHABETICAL_INDEX)
    set(DOXYGEN_ALPHABETICAL_INDEX YES)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_HTML)
    set(DOXYGEN_GENERATE_HTML YES)
endif()
if(NOT DEFINED DOXYGEN_HTML_OUTPUT)
    set(DOXYGEN_HTML_OUTPUT html)
endif()
if(NOT DEFINED DOXYGEN_HTML_FILE_EXTENSION)
    set(DOXYGEN_HTML_FILE_EXTENSION .html)
endif()
if(NOT DEFINED DOXYGEN_HTML_COLORSTYLE_HUE)
    set(DOXYGEN_HTML_COLORSTYLE_HUE 220)
endif()
if(NOT DEFINED DOXYGEN_HTML_COLORSTYLE_SAT)
    set(DOXYGEN_HTML_COLORSTYLE_SAT 100)
endif()
if(NOT DEFINED DOXYGEN_HTML_COLORSTYLE_GAMMA)
    set(DOXYGEN_HTML_COLORSTYLE_GAMMA 80)
endif()
if(NOT DEFINED DOXYGEN_HTML_TIMESTAMP)
    set(DOXYGEN_HTML_TIMESTAMP NO)
endif()
if(NOT DEFINED DOXYGEN_HTML_DYNAMIC_MENUS)
    set(DOXYGEN_HTML_DYNAMIC_MENUS YES)
endif()
if(NOT DEFINED DOXYGEN_HTML_DYNAMIC_SECTIONS)
    set(DOXYGEN_HTML_DYNAMIC_SECTIONS NO)
endif()
if(NOT DEFINED DOXYGEN_HTML_INDEX_NUM_ENTRIES)
    set(DOXYGEN_HTML_INDEX_NUM_ENTRIES 100)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_DOCSET)
    set(DOXYGEN_GENERATE_DOCSET NO)
endif()
if(NOT DEFINED DOXYGEN_DOCSET_FEEDNAME)
    set(DOXYGEN_DOCSET_FEEDNAME "Doxygen generated docs")
endif()
if(NOT DEFINED DOXYGEN_DOCSET_BUNDLE_ID)
    set(DOXYGEN_DOCSET_BUNDLE_ID org.doxygen.Project)
endif()
if(NOT DEFINED DOXYGEN_DOCSET_PUBLISHER_ID)
    set(DOXYGEN_DOCSET_PUBLISHER_ID org.doxygen.Publisher)
endif()
if(NOT DEFINED DOXYGEN_DOCSET_PUBLISHER_NAME)
    set(DOXYGEN_DOCSET_PUBLISHER_NAME Publisher)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_HTMLHELP)
    set(DOXYGEN_GENERATE_HTMLHELP NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_CHI)
    set(DOXYGEN_GENERATE_CHI NO)
endif()
if(NOT DEFINED DOXYGEN_BINARY_TOC)
    set(DOXYGEN_BINARY_TOC NO)
endif()
if(NOT DEFINED DOXYGEN_TOC_EXPAND)
    set(DOXYGEN_TOC_EXPAND NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_QHP)
    set(DOXYGEN_GENERATE_QHP NO)
endif()
if(NOT DEFINED DOXYGEN_QHP_NAMESPACE)
    set(DOXYGEN_QHP_NAMESPACE org.doxygen.Project)
endif()
if(NOT DEFINED DOXYGEN_QHP_VIRTUAL_FOLDER)
    set(DOXYGEN_QHP_VIRTUAL_FOLDER doc)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_ECLIPSEHELP)
    set(DOXYGEN_GENERATE_ECLIPSEHELP NO)
endif()
if(NOT DEFINED DOXYGEN_ECLIPSE_DOC_ID)
    set(DOXYGEN_ECLIPSE_DOC_ID org.doxygen.Project)
endif()
if(NOT DEFINED DOXYGEN_DISABLE_INDEX)
    set(DOXYGEN_DISABLE_INDEX NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_TREEVIEW)
    set(DOXYGEN_GENERATE_TREEVIEW NO)
endif()
if(NOT DEFINED DOXYGEN_ENUM_VALUES_PER_LINE)
    set(DOXYGEN_ENUM_VALUES_PER_LINE 4)
endif()
if(NOT DEFINED DOXYGEN_TREEVIEW_WIDTH)
    set(DOXYGEN_TREEVIEW_WIDTH 250)
endif()
if(NOT DEFINED DOXYGEN_EXT_LINKS_IN_WINDOW)
    set(DOXYGEN_EXT_LINKS_IN_WINDOW NO)
endif()
if(NOT DEFINED DOXYGEN_HTML_FORMULA_FORMAT)
    set(DOXYGEN_HTML_FORMULA_FORMAT png)
endif()
if(NOT DEFINED DOXYGEN_FORMULA_FONTSIZE)
    set(DOXYGEN_FORMULA_FONTSIZE 10)
endif()
if(NOT DEFINED DOXYGEN_FORMULA_TRANSPARENT)
    set(DOXYGEN_FORMULA_TRANSPARENT YES)
endif()
if(NOT DEFINED DOXYGEN_USE_MATHJAX)
    set(DOXYGEN_USE_MATHJAX NO)
endif()
if(NOT DEFINED DOXYGEN_MATHJAX_FORMAT)
    set(DOXYGEN_MATHJAX_FORMAT HTML-CSS)
endif()
if(NOT DEFINED DOXYGEN_MATHJAX_RELPATH)
    set(DOXYGEN_MATHJAX_RELPATH https://cdn.jsdelivr.net/npm/mathjax@2)
endif()
if(NOT DEFINED DOXYGEN_SEARCHENGINE)
    set(DOXYGEN_SEARCHENGINE YES)
endif()
if(NOT DEFINED DOXYGEN_SERVER_BASED_SEARCH)
    set(DOXYGEN_SERVER_BASED_SEARCH NO)
endif()
if(NOT DEFINED DOXYGEN_EXTERNAL_SEARCH)
    set(DOXYGEN_EXTERNAL_SEARCH NO)
endif()
if(NOT DEFINED DOXYGEN_SEARCHDATA_FILE)
    set(DOXYGEN_SEARCHDATA_FILE searchdata.xml)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_LATEX)
    set(DOXYGEN_GENERATE_LATEX YES)
endif()
if(NOT DEFINED DOXYGEN_LATEX_OUTPUT)
    set(DOXYGEN_LATEX_OUTPUT latex)
endif()
if(NOT DEFINED DOXYGEN_MAKEINDEX_CMD_NAME)
    set(DOXYGEN_MAKEINDEX_CMD_NAME makeindex)
endif()
if(NOT DEFINED DOXYGEN_LATEX_MAKEINDEX_CMD)
    set(DOXYGEN_LATEX_MAKEINDEX_CMD makeindex)
endif()
if(NOT DEFINED DOXYGEN_COMPACT_LATEX)
    set(DOXYGEN_COMPACT_LATEX NO)
endif()
if(NOT DEFINED DOXYGEN_PAPER_TYPE)
    set(DOXYGEN_PAPER_TYPE a4)
endif()
if(NOT DEFINED DOXYGEN_PDF_HYPERLINKS)
    set(DOXYGEN_PDF_HYPERLINKS YES)
endif()
if(NOT DEFINED DOXYGEN_USE_PDFLATEX)
    set(DOXYGEN_USE_PDFLATEX YES)
endif()
if(NOT DEFINED DOXYGEN_LATEX_BATCHMODE)
    set(DOXYGEN_LATEX_BATCHMODE NO)
endif()
if(NOT DEFINED DOXYGEN_LATEX_HIDE_INDICES)
    set(DOXYGEN_LATEX_HIDE_INDICES NO)
endif()
if(NOT DEFINED DOXYGEN_LATEX_SOURCE_CODE)
    set(DOXYGEN_LATEX_SOURCE_CODE NO)
endif()
if(NOT DEFINED DOXYGEN_LATEX_BIB_STYLE)
    set(DOXYGEN_LATEX_BIB_STYLE plain)
endif()
if(NOT DEFINED DOXYGEN_LATEX_TIMESTAMP)
    set(DOXYGEN_LATEX_TIMESTAMP NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_RTF)
    set(DOXYGEN_GENERATE_RTF NO)
endif()
if(NOT DEFINED DOXYGEN_RTF_OUTPUT)
    set(DOXYGEN_RTF_OUTPUT rtf)
endif()
if(NOT DEFINED DOXYGEN_COMPACT_RTF)
    set(DOXYGEN_COMPACT_RTF NO)
endif()
if(NOT DEFINED DOXYGEN_RTF_HYPERLINKS)
    set(DOXYGEN_RTF_HYPERLINKS NO)
endif()
if(NOT DEFINED DOXYGEN_RTF_SOURCE_CODE)
    set(DOXYGEN_RTF_SOURCE_CODE NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_MAN)
    set(DOXYGEN_GENERATE_MAN NO)
endif()
if(NOT DEFINED DOXYGEN_MAN_OUTPUT)
    set(DOXYGEN_MAN_OUTPUT man)
endif()
if(NOT DEFINED DOXYGEN_MAN_EXTENSION)
    set(DOXYGEN_MAN_EXTENSION .3)
endif()
if(NOT DEFINED DOXYGEN_MAN_LINKS)
    set(DOXYGEN_MAN_LINKS NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_XML)
    set(DOXYGEN_GENERATE_XML NO)
endif()
if(NOT DEFINED DOXYGEN_XML_OUTPUT)
    set(DOXYGEN_XML_OUTPUT xml)
endif()
if(NOT DEFINED DOXYGEN_XML_PROGRAMLISTING)
    set(DOXYGEN_XML_PROGRAMLISTING YES)
endif()
if(NOT DEFINED DOXYGEN_XML_NS_MEMB_FILE_SCOPE)
    set(DOXYGEN_XML_NS_MEMB_FILE_SCOPE NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_DOCBOOK)
    set(DOXYGEN_GENERATE_DOCBOOK NO)
endif()
if(NOT DEFINED DOXYGEN_DOCBOOK_OUTPUT)
    set(DOXYGEN_DOCBOOK_OUTPUT docbook)
endif()
if(NOT DEFINED DOXYGEN_DOCBOOK_PROGRAMLISTING)
    set(DOXYGEN_DOCBOOK_PROGRAMLISTING NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_AUTOGEN_DEF)
    set(DOXYGEN_GENERATE_AUTOGEN_DEF NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_PERLMOD)
    set(DOXYGEN_GENERATE_PERLMOD NO)
endif()
if(NOT DEFINED DOXYGEN_PERLMOD_LATEX)
    set(DOXYGEN_PERLMOD_LATEX NO)
endif()
if(NOT DEFINED DOXYGEN_PERLMOD_PRETTY)
    set(DOXYGEN_PERLMOD_PRETTY YES)
endif()
if(NOT DEFINED DOXYGEN_ENABLE_PREPROCESSING)
    set(DOXYGEN_ENABLE_PREPROCESSING YES)
endif()
if(NOT DEFINED DOXYGEN_MACRO_EXPANSION)
    set(DOXYGEN_MACRO_EXPANSION NO)
endif()
if(NOT DEFINED DOXYGEN_EXPAND_ONLY_PREDEF)
    set(DOXYGEN_EXPAND_ONLY_PREDEF NO)
endif()
if(NOT DEFINED DOXYGEN_SEARCH_INCLUDES)
    set(DOXYGEN_SEARCH_INCLUDES YES)
endif()
if(NOT DEFINED DOXYGEN_SKIP_FUNCTION_MACROS)
    set(DOXYGEN_SKIP_FUNCTION_MACROS YES)
endif()
if(NOT DEFINED DOXYGEN_ALLEXTERNALS)
    set(DOXYGEN_ALLEXTERNALS NO)
endif()
if(NOT DEFINED DOXYGEN_EXTERNAL_GROUPS)
    set(DOXYGEN_EXTERNAL_GROUPS YES)
endif()
if(NOT DEFINED DOXYGEN_EXTERNAL_PAGES)
    set(DOXYGEN_EXTERNAL_PAGES YES)
endif()
if(NOT DEFINED DOXYGEN_CLASS_DIAGRAMS)
    set(DOXYGEN_CLASS_DIAGRAMS YES)
endif()
if(NOT DEFINED DOXYGEN_HIDE_UNDOC_RELATIONS)
    set(DOXYGEN_HIDE_UNDOC_RELATIONS YES)
endif()
if(NOT DEFINED DOXYGEN_HAVE_DOT)
    set(DOXYGEN_HAVE_DOT YES)
endif()
if(NOT DEFINED DOXYGEN_DOT_NUM_THREADS)
    set(DOXYGEN_DOT_NUM_THREADS 0)
endif()
if(NOT DEFINED DOXYGEN_DOT_FONTNAME)
    set(DOXYGEN_DOT_FONTNAME Helvetica)
endif()
if(NOT DEFINED DOXYGEN_DOT_FONTSIZE)
    set(DOXYGEN_DOT_FONTSIZE 10)
endif()
if(NOT DEFINED DOXYGEN_CLASS_GRAPH)
    set(DOXYGEN_CLASS_GRAPH YES)
endif()
if(NOT DEFINED DOXYGEN_COLLABORATION_GRAPH)
    set(DOXYGEN_COLLABORATION_GRAPH YES)
endif()
if(NOT DEFINED DOXYGEN_GROUP_GRAPHS)
    set(DOXYGEN_GROUP_GRAPHS YES)
endif()
if(NOT DEFINED DOXYGEN_UML_LOOK)
    set(DOXYGEN_UML_LOOK NO)
endif()
if(NOT DEFINED DOXYGEN_UML_LIMIT_NUM_FIELDS)
    set(DOXYGEN_UML_LIMIT_NUM_FIELDS 10)
endif()
if(NOT DEFINED DOXYGEN_DOT_UML_DETAILS)
    set(DOXYGEN_DOT_UML_DETAILS NO)
endif()
if(NOT DEFINED DOXYGEN_DOT_WRAP_THRESHOLD)
    set(DOXYGEN_DOT_WRAP_THRESHOLD 17)
endif()
if(NOT DEFINED DOXYGEN_TEMPLATE_RELATIONS)
    set(DOXYGEN_TEMPLATE_RELATIONS NO)
endif()
if(NOT DEFINED DOXYGEN_INCLUDE_GRAPH)
    set(DOXYGEN_INCLUDE_GRAPH YES)
endif()
if(NOT DEFINED DOXYGEN_INCLUDED_BY_GRAPH)
    set(DOXYGEN_INCLUDED_BY_GRAPH YES)
endif()
if(NOT DEFINED DOXYGEN_CALL_GRAPH)
    set(DOXYGEN_CALL_GRAPH NO)
endif()
if(NOT DEFINED DOXYGEN_CALLER_GRAPH)
    set(DOXYGEN_CALLER_GRAPH NO)
endif()
if(NOT DEFINED DOXYGEN_GRAPHICAL_HIERARCHY)
    set(DOXYGEN_GRAPHICAL_HIERARCHY YES)
endif()
if(NOT DEFINED DOXYGEN_DIRECTORY_GRAPH)
    set(DOXYGEN_DIRECTORY_GRAPH YES)
endif()
if(NOT DEFINED DOXYGEN_DOT_IMAGE_FORMAT)
    set(DOXYGEN_DOT_IMAGE_FORMAT png)
endif()
if(NOT DEFINED DOXYGEN_INTERACTIVE_SVG)
    set(DOXYGEN_INTERACTIVE_SVG NO)
endif()
if(NOT DEFINED DOXYGEN_DOT_GRAPH_MAX_NODES)
    set(DOXYGEN_DOT_GRAPH_MAX_NODES 50)
endif()
if(NOT DEFINED DOXYGEN_MAX_DOT_GRAPH_DEPTH)
    set(DOXYGEN_MAX_DOT_GRAPH_DEPTH 0)
endif()
if(NOT DEFINED DOXYGEN_DOT_TRANSPARENT)
    set(DOXYGEN_DOT_TRANSPARENT NO)
endif()
if(NOT DEFINED DOXYGEN_DOT_MULTI_TARGETS)
    set(DOXYGEN_DOT_MULTI_TARGETS NO)
endif()
if(NOT DEFINED DOXYGEN_GENERATE_LEGEND)
    set(DOXYGEN_GENERATE_LEGEND YES)
endif()
if(NOT DEFINED DOXYGEN_DOT_CLEANUP)
    set(DOXYGEN_DOT_CLEANUP YES)
endif()
