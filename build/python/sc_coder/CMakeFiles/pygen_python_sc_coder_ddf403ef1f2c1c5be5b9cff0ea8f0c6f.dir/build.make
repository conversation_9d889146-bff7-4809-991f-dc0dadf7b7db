# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/gr-sc_coder

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/gr-sc_coder/build

# Utility rule file for pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.

# Include any custom commands dependencies for this target.
include python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/compiler_depend.make

# Include the progress variables for this target.
include python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/progress.make

python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f: python/sc_coder/__init__.pyc
python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f: python/sc_coder/sc_encoder.pyc

python/sc_coder/__init__.pyc: ../python/sc_coder/__init__.py
python/sc_coder/__init__.pyc: ../python/sc_coder/sc_encoder.py
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/gr-sc_coder/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating __init__.pyc, sc_encoder.pyc"
	cd /home/<USER>/gr-sc_coder/build/python/sc_coder && /usr/bin/python3 /home/<USER>/gr-sc_coder/build/python_compile_helper.py /home/<USER>/gr-sc_coder/python/sc_coder/__init__.py /home/<USER>/gr-sc_coder/python/sc_coder/sc_encoder.py /home/<USER>/gr-sc_coder/build/python/sc_coder/__init__.pyc /home/<USER>/gr-sc_coder/build/python/sc_coder/sc_encoder.pyc

python/sc_coder/sc_encoder.pyc: python/sc_coder/__init__.pyc
	@$(CMAKE_COMMAND) -E touch_nocreate python/sc_coder/sc_encoder.pyc

pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f: python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f
pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f: python/sc_coder/__init__.pyc
pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f: python/sc_coder/sc_encoder.pyc
pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f: python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/build.make
.PHONY : pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f

# Rule to build all files generated by this target.
python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/build: pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f
.PHONY : python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/build

python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/clean:
	cd /home/<USER>/gr-sc_coder/build/python/sc_coder && $(CMAKE_COMMAND) -P CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/cmake_clean.cmake
.PHONY : python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/clean

python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/depend:
	cd /home/<USER>/gr-sc_coder/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/gr-sc_coder /home/<USER>/gr-sc_coder/python/sc_coder /home/<USER>/gr-sc_coder/build /home/<USER>/gr-sc_coder/build/python/sc_coder /home/<USER>/gr-sc_coder/build/python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : python/sc_coder/CMakeFiles/pygen_python_sc_coder_ddf403ef1f2c1c5be5b9cff0ea8f0c6f.dir/depend

